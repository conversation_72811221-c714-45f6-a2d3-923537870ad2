"use client";

import React, { useState } from "react";
import {
	<PERSON>ton,
	<PERSON>alog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	Typography,
	Box,
	CircularProgress,
} from "@mui/material";
import { MessageCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useCreateConversationMutation } from "@/store/api/messageApi";
import toast from "react-hot-toast";

interface StartConversationButtonProps {
	recipientId: string;
	recipientName: string;
	recipientRole: "donor" | "organization";
	relatedDonation?: string;
	relatedCause?: string;
	variant?: "button" | "icon";
	size?: "small" | "medium" | "large";
	fullWidth?: boolean;
}

const StartConversationButton: React.FC<StartConversationButtonProps> = ({
	recipientId,
	recipientName,
	recipientRole,
	relatedDonation,
	relatedCause,
	variant = "button",
	size = "medium",
	fullWidth = false,
}) => {
	const router = useRouter();
	const { user } = useSelector((state: RootState) => state.auth);
	const [open, setOpen] = useState(false);
	const [message, setMessage] = useState("");
	
	const [createConversation, { isLoading }] = useCreateConversationMutation();

	// Don't show button if trying to message yourself
	if (user?.id === recipientId) {
		return null;
	}

	const handleOpen = () => {
		setOpen(true);
		// Set default message based on context
		if (relatedDonation) {
			setMessage(`Hi ${recipientName}, I'd like to discuss the donation...`);
		} else if (relatedCause) {
			setMessage(`Hi ${recipientName}, I'm interested in your cause and would like to know more...`);
		} else {
			setMessage(`Hi ${recipientName}, `);
		}
	};

	const handleClose = () => {
		setOpen(false);
		setMessage("");
	};

	const handleSendMessage = async () => {
		if (!message.trim()) {
			toast.error("Please enter a message");
			return;
		}

		try {
			const result = await createConversation({
				participantId: recipientId,
				initialMessage: message.trim(),
				relatedDonation,
				relatedCause,
			}).unwrap();

			toast.success("Conversation started!");
			handleClose();
			
			// Navigate to the new conversation
			router.push(`/dashboard/messages/${result.data._id}`);
		} catch (error: any) {
			toast.error(error?.data?.message || "Failed to start conversation");
		}
	};

	const buttonContent = variant === "icon" ? (
		<MessageCircle size={size === "small" ? 16 : size === "large" ? 24 : 20} />
	) : (
		<>
			<MessageCircle size={16} style={{ marginRight: 8 }} />
			Message {recipientRole === "organization" ? "Organization" : "Donor"}
		</>
	);

	return (
		<>
			<Button
				variant={variant === "icon" ? "outlined" : "contained"}
				color="primary"
				size={size}
				fullWidth={fullWidth}
				onClick={handleOpen}
				sx={{
					...(variant === "icon" && {
						minWidth: "auto",
						width: size === "small" ? 32 : size === "large" ? 48 : 40,
						height: size === "small" ? 32 : size === "large" ? 48 : 40,
						borderRadius: "50%",
					}),
				}}
			>
				{buttonContent}
			</Button>

			<Dialog
				open={open}
				onClose={handleClose}
				maxWidth="sm"
				fullWidth
				PaperProps={{
					sx: { borderRadius: 2 },
				}}
			>
				<DialogTitle>
					<Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
						<MessageCircle size={24} />
						<Typography variant="h6">
							Start Conversation with {recipientName}
						</Typography>
					</Box>
				</DialogTitle>

				<DialogContent>
					<Box sx={{ mb: 2 }}>
						<Typography variant="body2" color="text.secondary">
							Send a message to {recipientName} ({recipientRole})
							{relatedDonation && " about this donation"}
							{relatedCause && " about this cause"}
						</Typography>
					</Box>

					<TextField
						fullWidth
						multiline
						rows={4}
						label="Your message"
						value={message}
						onChange={(e) => setMessage(e.target.value)}
						placeholder={`Type your message to ${recipientName}...`}
						disabled={isLoading}
						sx={{
							"& .MuiOutlinedInput-root": {
								borderRadius: 2,
							},
						}}
					/>

					{(relatedDonation || relatedCause) && (
						<Box
							sx={{
								mt: 2,
								p: 2,
								backgroundColor: "primary.50",
								borderRadius: 1,
								border: 1,
								borderColor: "primary.200",
							}}
						>
							<Typography variant="caption" color="primary.main" sx={{ fontWeight: 600 }}>
								Context:
							</Typography>
							<Typography variant="body2" color="text.secondary">
								{relatedDonation && "This conversation is related to a donation"}
								{relatedCause && "This conversation is related to a cause"}
							</Typography>
						</Box>
					)}
				</DialogContent>

				<DialogActions sx={{ p: 3, pt: 1 }}>
					<Button
						onClick={handleClose}
						disabled={isLoading}
						color="inherit"
					>
						Cancel
					</Button>
					<Button
						onClick={handleSendMessage}
						disabled={isLoading || !message.trim()}
						variant="contained"
						startIcon={
							isLoading ? (
								<CircularProgress size={16} color="inherit" />
							) : (
								<MessageCircle size={16} />
							)
						}
					>
						{isLoading ? "Sending..." : "Send Message"}
					</Button>
				</DialogActions>
			</Dialog>
		</>
	);
};

export default StartConversationButton;
